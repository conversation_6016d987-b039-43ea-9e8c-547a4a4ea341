<?php $__env->startSection('title', 'My Upload'); ?>

<?php $__env->startSection('css_page'); ?>
<!-- BEGIN VENDOR CSS-->
<!-- END VENDOR CSS-->

<!-- BEGIN Page Level CSS-->
<style>
  /* width */
  ::-webkit-scrollbar {
    width: 5px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #c8c8c8;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* color danger */
  .color-danger {
    color: #ff0000;
  }

  .text-danger {
    color: #F64E60 !important;
  }
</style>
<!-- END Page Level CSS-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="content classMasterData d-flex flex-column flex-column-fluid" style="padding-top: 10px !important; padding-bottom: 0px !important;" id="kt_content">
  <!--begin::Subheader-->
  
  <!--end::Subheader-->
  <!--begin::Entry-->
  <div class="d-flex flex-column-fluid">
    <!--begin::Container-->
    <div class="container-fluid">
      <!--begin::Notice-->
      <!--end::Notice-->
      <!--begin::Card-->
      <div class="card card-custom margin-bot-card">
        <div class="card-header flex-wrap py-3">
          <div class="card-title">
            <h3 class="card-label">Data Upload</h3>
            <span class="d-block text-muted pt-2 font-size-sm"></span>
            </h3>
          </div>
          <div class="card-toolbar">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-C')): ?>
            <!--begin::Button-->
            <button id="addMenu" name="addMenu" class="btn btn-primary font-weight-bolder">
              <span class="svg-icon svg-icon-md">
                <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                <!--end::Svg Icon-->
              </span>Upload Dokumen
            </button>
            <?php endif; ?>
            <!--end::Button-->
          </div>
        </div>
        <div class="card-body">
          <!--begin: Search Form-->
          <!--begin::Search Form-->
          <div class="mb-7">
            <div class="row align-items-center">
              <div class="col-lg-9 col-xl-8">
                <div class="row align-items-center">
                  <div class="col-md-4 my-2 my-md-0">
                    <div class="input-icon">
                      <input type="text" class="form-control" placeholder="Search..." id="kt_datatable_search_query" />
                      <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                      </span>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-danger font-weight-bold px-6" id="btn-search">Search</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--end::Search Form-->
          <!--end: Search Form-->
          <!--begin: Datatable-->
          <div class="datatable datatable-bordered datatable-head-custom " id="kt_datatable_menu"></div>
          <!--end: Datatable-->
        </div>
      </div>
      <!--end::Card-->
    </div>
    <!--end::Container-->
  </div>
  <!--end::Entry-->
</div>

<!--begin:Modal-->
<div class="modal fade" id="modalMenu" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalMenuTitle">Upload</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i aria-hidden="true" class="ki ki-close"></i>
        </button>
      </div>
      <!--begin:Form-->
      <div class="modal-body" style="height: 400px;overflow-x: hidden;">
        <form role="form" class="form" name="formmenus" id="formmenus" enctype="multipart/formdata" method="">
          <div class="mb-7">
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Title<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                <input type="text" class="form-control" id="title" name="title"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Category<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                <select class="form-control select2" name="category_id" id="category_id" style="width: 100%;">
                  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <option value="<?php echo e($detail['id']); ?>"><?php echo $detail['name']; ?></option>
                  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Description<span class="color-danger"></span>:</label>
              <div class="col-lg-9">
                  <textarea class="form-control" id="description" name="description" ></textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">File<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                  <input type="file" accept="application/pdf,image/*,video/mp4" class="form-control" id="file" name="file" >
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-light-danger font-weight-bold" data-dismiss="modal"><i
            class="fa fa-times"></i>Cancel
        </button>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(['my-upload-C' , 'my-upload-U'])): ?>
        <button type="submit" id="saveMenu" data-id="" class="btn btn-success font-weight-bold">
          <i class="fa fa-save"></i> Save changes
        </button>
        <?php endif; ?>
      </div>
      <!--end:Form-->
    </div>
  </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js_page'); ?>
<!--begin::Page Vendors(used by this page)-->
<!--end::Page Vendors-->
<!--begin::Page Scripts(used by this page)-->
<!--end::Page Scripts-->

<script type="text/javascript">
  $(document).ready(function () {
    if (document.getElementsByClassName("classMasterData")) {
        var element = document.getElementById("kt_wrapper");
        element.classList.add("headerSync");
    }
    $('.select2').select2();

    var datatable = $('#kt_datatable_menu');

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-R')): ?>

    datatable.KTDatatable({
        // datasource definition
        data: {
            type: 'remote',
            source: {
                read: {
                    url: '/my-upload/list',
                    method: 'GET',
                }
            },
            pageSize: 10,
        },
        // layout definition
        layout: {
            scroll: false, // enable/disable datatable scroll both horizontal and vertical when needed.
            footer: false // display/hide footer
        },
        // column sorting
        sortable: true,
        pagination: true,
        rows: {
                autoHide: false,
            },
        search: {
            input: $('#kt_datatable_search_query'),
            key: 'generalSearch',
            onEnter: true,
        },
        // columns definition
        columns: [
            {
                field: 'title',
                title: 'Title',
                width: 100,
            }, {
                field: 'category_name',
                title: 'Category',
                width: 100,
            }, {
                field: 'description',
                title: 'Description',
                width: 100,
            }, {
                field: 'status',
                title: 'Status',
                width: 100,
            }, {
                field: 'created_at',
                title: 'Tahun',
                width: 100,
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 240,
                autoHide: false,
                overflow: 'visible',
                template: function (row) {
                    let btnApprove = '';
                    if(row.status == 'Waiting'){
                        btnApprove = `<button type='button' class='approve btn btn-sm btn-icon btn-outline-warning ' title='Approve' data-toggle='tooltip' data-action='approve' data-id="${row.id}" ><i class='fa fa-check'></i> </button>
                        <button type='button' class='reject btn btn-sm btn-icon btn-outline-warning ' title='Reject' data-toggle='tooltip' data-action='reject' data-id="${row.id}" ><i class='fa fa-times'></i> </button>`;
                    }
                    return "<left>" +
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-R')): ?>
                                "<button type='button' class='preview btn btn-sm btn-icon btn-outline-warning ' title='Preview' data-toggle='tooltip' data-id=" + row.id + " ><i class='fa fa-eye'></i> </button>  " +
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-U')): ?>
                                "<button type='button' class='edits btn btn-sm btn-icon btn-outline-warning ' title='Edit' data-toggle='tooltip' data-id=" + row.id + " ><i class='fa fa-edit'></i> </button>  " +
                            <?php endif; ?>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-A')): ?>
                                btnApprove +
                            <?php endif; ?>
                            "</left>";
                },
            }
        ],

    });

    <?php endif; ?>

    $(document).on('click', '#saveMenu', function() {
        $('#formmenus').trigger('submit');
    })

    $(document).on('click', '#btn-search', function() {
        datatable.search($("#kt_datatable_search_query").val());
    })

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-C')): ?>
    $(document).on('click', '#addMenu', function () {
        $("#saveMenu").data("id", "");
        $('#modalMenuTitle').text('Upload Dokumen');
        $('#modalMenu').modal('show');
        $(`.form-control`).removeClass('is-invalid');
        $(`.invalid-feedback`).remove();
        let form = document.forms.formmenus; // <form name="formmenus"> element
        form.reset();
    });
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-A')): ?>
    $(document).on('click', '.approve,.reject', function () {
        let id = $(this).data('id');
        let action = $(this).data('action');
        let url = `./my-upload/${id}/${action}`;
        Swal.fire({
            title: "Konfirmasi",
            text: `Yakin akan melakukan ${action} ke dokumen ini`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    type: 'POST', 
                    url: url, 
                })
                .done(function (data) {
                    showtoastr('success', data.message);
                })
                .fail(function (data) {
                    show_toastr('error', data.responseJSON.status, data.responseJSON.message);
                    $.each(data.responseJSON.messages, function (index, value) {
                        show_toastr('error', index, value);
                    });
                })
                .always(function () {
                    datatable.reload();
                });
              }
            });
    });
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-U')): ?>
    $(document).on('click', '.edits', function () {
        $.ajax({
            type: 'GET', // define the type of HTTP verb we want to use (POST for our form)
            url: './my-upload/' + $(this).data('id'), // the url where we want to POST
            beforeSend: function () {
                let form = document.forms.formmenus; // <form name="formmenus"> element
                form.reset();
                $(`.form-control`).removeClass('is-invalid');
                $(`.invalid-feedback`).remove();
            }
        }).done(function (res) {
            let form = document.forms.formmenus; // <form name="formmenus"> element
            if (res.success) {
                showtoastr('success', res.message);
                $(form.elements.title).val(res.data.title);
                $(form.elements.description).val(res.data.description);
                $(form.elements.category_id).val(res.data.category_id);
                $("#saveMenu").data( "id", res.data.id);
            }
        }).fail(function (data) {
            show_toastr('error', data.responseJSON.status, data.responseJSON.message);
            $.each(data.responseJSON.errors, function (index, value) {
                show_toastr('error', index, value);
            });
        }).always(function () {
            $('#modalMenuTitle').text('Edit Dokumen');
            $('#modalMenu').modal('show');
        });
    });
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check(['my-upload-C', 'my-upload-U'])): ?>
    $('#formmenus').submit(function (e) {
        e.preventDefault();
        var formData = new FormData($("#formmenus")[0]);
        // var formData = $('#formmenus').serializeArray(); // our data object
        var method = "POST";
        let menuID = $("#saveMenu").data("id");

        if (typeof menuID == "undefined" || menuID == "") {
            var url = `./my-upload`;
        } else {
            var url = `./my-upload/${menuID}/update`;
        }

        $.ajax({
            type: method, // define the type of HTTP verb we want to use (POST for our form)
            url: url, // the url where we want to POST
            data: formData,
            dataType: 'JSON', // what type of data do we expect back from the server
            contentType: false,
            processData: false,
            beforeSend: function () {
                $(`.form-control`).removeClass('is-invalid');
                $(`.invalid-feedback`).remove();
                $('#saveMenu').attr('disabled', true).html("<i class='fa fa-spinner fa-spin'></i> processing");
            }
        }).done(function (data) {
            $("#modalMenu").modal('hide');
            showtoastr('success', data.message);
            $("#saveMenu").data("id", "");
            $("#formmenus")[0].reset();
            menuID = "";
            let form = document.forms.formmenus; // <form name="formmenus"> element
            form.reset();
            datatable.reload();
        }).fail(function (data) {
            show_toastr('error', data.responseJSON.status, data.responseJSON.message);
            $.each(data.responseJSON.errors, function (index, value) {
                if ($(`input[name='${index}']`)) {
                    $(`input[name='${index}']`).addClass(`is-invalid`);
                    $(`input[name='${index}']`).after(`<div class="invalid-feedback">${value}</div>`);
                }
                show_toastr('error', index, value);
            });
        }).always(function () {
            $('#saveMenu').attr('disabled', false).html("<i class='fa fa-save'></i> Save");
        });
    });
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-D')): ?>
    $(document).on('click', '.deletes', function () {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        })
        .then(isConfirm => {
        if(isConfirm.isConfirmed) {
          $.ajax({
              type: 'DELETE', // define the type of HTTP verb we want to use (POST for our form)
              url: './my-upload/' + $(this).data('id'), // the url where we want to POST
          })
          .done(function (data) {
              showtoastr('success', data.message);
          })
          .fail(function (data) {
              show_toastr('error', data.responseJSON.status, data.responseJSON.message);
              $.each(data.responseJSON.messages, function (index, value) {
                  show_toastr('error', index, value);
              });
          })
          .always(function () {
              datatable.reload();
          });
        }
      });
    });
    <?php endif; ?>

    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('my-upload-R')): ?>
    $(document).on('click', '.preview', function () {
        const documentId = $(this).data('id');

        $.ajax({
            type: 'GET',
            url: './my-upload/' + documentId,
            beforeSend: function () {
                // Show loading indicator
                showtoastr('info', 'Loading document preview...');
            }
        }).done(function (res) {
            if (res.success && res.data) {
                const document = res.data;
                const filePath = document.path;
                const fileName = document.filename || '';

                if (!filePath) {
                    show_toastr('error', 'Error', 'File path not found');
                    return;
                }

                // Construct the file URL
                const fileUrl = '/storage/app/public/' + filePath;

                // Determine file type from filename extension
                const fileExtension = getFileExtension(fileName).toLowerCase();
                const fileType = determineFileType(fileExtension);

                // Handle preview based on file type
                handleFilePreview(fileUrl, fileType, fileName, document.title);

            } else {
                show_toastr('error', 'Error', 'Failed to load document data');
            }
        }).fail(function (data) {
            show_toastr('error', data.responseJSON?.status || 'Error',
                       data.responseJSON?.message || 'Failed to load document');
        });
    });

    // Helper function to get file extension
    function getFileExtension(filename) {
        if (!filename) return '';
        return filename.split('.').pop() || '';
    }

    // Helper function to determine file type category
    function determineFileType(extension) {
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
        const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        const documentExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'];

        if (imageExtensions.includes(extension)) {
            return 'image';
        } else if (videoExtensions.includes(extension)) {
            return 'video';
        } else if (documentExtensions.includes(extension)) {
            return 'document';
        } else {
            return 'unknown';
        }
    }

    // Helper function to handle file preview based on type
    function handleFilePreview(fileUrl, fileType, fileName, documentTitle) {
        try {
            switch (fileType) {
                case 'image':
                    openImagePreview(fileUrl, fileName, documentTitle);
                    break;
                case 'video':
                    openVideoPreview(fileUrl, fileName, documentTitle);
                    break;
                case 'document':
                    openDocumentPreview(fileUrl, fileName, documentTitle);
                    break;
                default:
                    // For unknown file types, try to open directly
                    openGenericPreview(fileUrl, fileName, documentTitle);
                    break;
            }
        } catch (error) {
            console.error('Preview error:', error);
            show_toastr('error', 'Preview Error', 'Failed to open file preview');
        }
    }

    // Function to open image preview in new tab
    function openImagePreview(fileUrl, fileName, documentTitle) {
        const newWindow = window.open('', '_blank');
        if (newWindow) {
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Preview: ${documentTitle || fileName}</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            background: #f5f5f5;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            font-family: Arial, sans-serif;
                        }
                        .container {
                            text-align: center;
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        img {
                            max-width: 100%;
                            max-height: 80vh;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                        }
                        h3 {
                            margin-top: 0;
                            color: #333;
                        }
                        .filename {
                            color: #666;
                            font-size: 14px;
                            margin-bottom: 15px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h3>${documentTitle || 'Image Preview'}</h3>
                        <div class="filename">${fileName}</div>
                        <img src="${fileUrl}" alt="${fileName}" onerror="this.parentElement.innerHTML='<p>Error loading image</p>'">
                    </div>
                </body>
                </html>
            `);
            newWindow.document.close();
        } else {
            show_toastr('error', 'Popup Blocked', 'Please allow popups for this site');
        }
    }

    // Function to open video preview in new tab
    function openVideoPreview(fileUrl, fileName, documentTitle) {
        const newWindow = window.open('', '_blank');
        if (newWindow) {
            newWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Preview: ${documentTitle || fileName}</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            background: #f5f5f5;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            font-family: Arial, sans-serif;
                        }
                        .container {
                            text-align: center;
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        video {
                            max-width: 100%;
                            max-height: 80vh;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                        }
                        h3 {
                            margin-top: 0;
                            color: #333;
                        }
                        .filename {
                            color: #666;
                            font-size: 14px;
                            margin-bottom: 15px;
                        }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <h3>${documentTitle || 'Video Preview'}</h3>
                        <div class="filename">${fileName}</div>
                        <video controls>
                            <source src="${fileUrl}" type="video/mp4">
                            <p>Your browser does not support the video tag. <a href="${fileUrl}" target="_blank">Download the video</a></p>
                        </video>
                    </div>
                </body>
                </html>
            `);
            newWindow.document.close();
        } else {
            show_toastr('error', 'Popup Blocked', 'Please allow popups for this site');
        }
    }

    // Function to open document preview in new tab
    function openDocumentPreview(fileUrl, fileName, documentTitle) {
        const extension = getFileExtension(fileName).toLowerCase();

        if (extension === 'pdf') {
            // For PDF files, open directly in browser
            window.open(fileUrl, '_blank');
        } else {
            // For other document types, create a preview page with download option
            const newWindow = window.open('', '_blank');
            if (newWindow) {
                newWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Preview: ${documentTitle || fileName}</title>
                        <style>
                            body {
                                margin: 0;
                                padding: 40px;
                                background: #f5f5f5;
                                font-family: Arial, sans-serif;
                                display: flex;
                                justify-content: center;
                                align-items: center;
                                min-height: 100vh;
                            }
                            .container {
                                text-align: center;
                                background: white;
                                padding: 40px;
                                border-radius: 8px;
                                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                                max-width: 500px;
                            }
                            h3 {
                                margin-top: 0;
                                color: #333;
                            }
                            .filename {
                                color: #666;
                                font-size: 14px;
                                margin-bottom: 20px;
                            }
                            .download-btn {
                                background: #007bff;
                                color: white;
                                padding: 12px 24px;
                                border: none;
                                border-radius: 4px;
                                text-decoration: none;
                                display: inline-block;
                                font-size: 16px;
                                cursor: pointer;
                                transition: background 0.3s;
                            }
                            .download-btn:hover {
                                background: #0056b3;
                            }
                            .file-icon {
                                font-size: 48px;
                                margin-bottom: 20px;
                                color: #007bff;
                            }
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="file-icon">📄</div>
                            <h3>${documentTitle || 'Document Preview'}</h3>
                            <div class="filename">${fileName}</div>
                            <p>This document type cannot be previewed directly in the browser.</p>
                            <a href="${fileUrl}" class="download-btn" target="_blank">Download & Open</a>
                        </div>
                    </body>
                    </html>
                `);
                newWindow.document.close();
            } else {
                // Fallback: direct download
                window.open(fileUrl, '_blank');
            }
        }
    }

    // Function to open generic preview for unknown file types
    function openGenericPreview(fileUrl, fileName, documentTitle) {
        // Try to open directly first
        const newWindow = window.open(fileUrl, '_blank');

        if (!newWindow) {
            show_toastr('error', 'Popup Blocked', 'Please allow popups for this site');
        }
    }
    <?php endif; ?>
  });


</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\dev-dmm\resources\views/myUpload.blade.php ENDPATH**/ ?>